# Overview

Product name: Voji

Vision: Voji is a mobile / web app that helps the user quickly capture ideas with their voice and turn them into complete stories, powered by Generative AI.

# Data models

## Ideabook

An ideabook is a collection of ideas.

Ideabook has the following associated data:
* Short name: Usually summarized by AI. User can edit it manually.
* Color: color of the ideabook for flexible categorization.
* Lock: whether the ideabook is locked.
* Created time: when the ideabook is created.

Ideabook is the container of the following data models:
* Idea - user input captured by voice recording or manual input text.
* Note - LLM answer saved by user as a note. Note has a title and text body.
* Chat - user can chat with <PERSON>M about their ideabook. Ideas will be used as context input to LLM conversation. Chat history is stored on user local device for fast loading.

### Color system

The Color system is a flexible way to allow user categorize their ideabooks.

Voji supports 6 colors in total. When user creates a new ideabook, Voji will use AI to determine the color based on the content of the ideabook. The color is determined by the following rules:
* `red`: work, professional, etc.
* `green`: family, friends, social, leisure, entertainment, sports, hobbies, etc.
* `blue`: personal growth, health, time management, etc.
* `yellow`: inspiration, diary, daily log, journal, etc.
* `orange`: travel, planning, birthday, parties, wedding, etc.
* `purple`: everything else but try to much other colors if possible.

But user can always change the color manually and decides how they want to categorize their ideabooks by color.

### Lock

An ideabook can be locked. User can click the lock button to lock / unlock an ideabook. If the user tries to lock a ideabook for the very first time of the entire app lifespan, they are prompted to enter and confirm a passcode. A single passcode is used to lock / unlock all ideabooks. Voji doesn't support per-ideabook passcode.

When an ideabook is locked, the following actions require a passcode:
* Access the ideabook content, to see all the ideas, notes, chat in that ideabook.
* Unlock the ideabook.
* User needs to type in the passcode to lock an ideabook except for the very first time when they lock any ideabook (at that they're prompted to enter a new passcode).

The following actions do not require a passcode:
* Ideabook short name is still visible in the Ideabooks list page.
* Add new idea in Ideabooks list page.
* Change color.

In the Ideabooks list page, there is no obvious indication of which Ideabook is locked to respect user's privacy. The only visual indication is in the context menu, based on if the lock or unlock button is shown.

The passcode is not stored in cloud. An encrypted salt (a fingerprint of the passcode) is stored in Firestore to support multi devices passcode verification for locked ideabooks. Voji backend does not have access to the passcode nor provides any way to retrieve it. User is clearly reminded that they are responsible for keeping the passcode safe and Voji cannot help retrieve it if lost.

## Idea

An idea is a user input captured by voice recording or manual input text. An idea always belong to one of the Ideabooks.

Idea has the following associated data:
* Content: the transcribed, LLM-refined text, or user manually edited text.
* Creation timestamp: when this idea is created initially.

User can freely create, edit, delete any idea in any ideabook they own. User can manually reorder the ideas within an ideabook. The order is saved in Firestore for persistence over multiple devices.

## Note

A note is an LLM answer saved by user as a note. A note always belong to one of the Ideabooks.

Note has the following associated data:
* Title: the user's original prompt.
* Content: the LLM response.
* Creation timestamp: when this note is created initially.

User can freely save note, edit the title, delete any note in any ideabook they own. User can manually reorder the notes within an ideabook. The order is saved in Firestore for persistence over multiple devices.

## Chat

A chat is a conversation with LLM about an ideabook. A chat always belong to one of the Ideabooks. Chat is a list of messages between user and LLM. Chat is saved locally on user's device only. Chat is not synced to cloud. Chat is not preserved when user switch device or reinstall the app.

# UI Features

## Welcome page

This is the first page user sees when they open the app for the first time. The purpose of the welcome page is to ask user sign in with their social account before they can continue to use the app. Google authentication is the only supported authentication method for now.

The welcome page has the following elements:
* Logo: Voji in Pacifico font.
* Slogan: "Ideas Spoken. Stories Born". in signika google font.
* Sign in to Continue, in Afacad font.
* Only 1 sign option: "Sign in with Google"
* Link to terms of service and privacy policy. User is clearly reminded that by signing in, they agree to the terms of service and privacy policy.

## Ideabook list page

This is the main page of the app.

A top bar:
* To the left: The logo "Voji"
* To the right: a user profile picture as a button to open a menu.

The menu supports the following actions:
* Upgrade to Voji PRO
* Settings
* Filter by color
* Sort by created time: asc / desc
* Group by color

A list of ideabooks. Each ideabook in the list shows several elements:
* Color: color of the ideabook for flexible categorization.
* Short name: Usually summarized by AI. User can edit it manually.
* A microphone button: click it will trigger recording of new idea that will be saved into the ideabook.
* Swipe to left will show more context buttons.
* Swipe to right will show color selection.

The expanded context buttons:
* The same microphone button.
* A pencil button to edit the ideabook short name.
* A lock / unlock button lock / unlock the ideabook, depending on if the ideabook is locked already.
* A trash bin button to delete the ideabook (triggers a confirmation dialog before deletion).

The color selection:
* A list of colors to choose from.

At the bottom of the ideabook list page is the "New Ideabook" button. Click it will trigger voice recording to create new ideabook.

## Settings page

The settings page is accessible from the menu in the top bar of the ideabooks list page.

The settings page has the following elements:
* A section for user profile:
  * User's name and email.
  * A button to sign out.
* A section for app settings:
  * A button to change the app passcode (if user already set one).
  * A button to change the app theme (light / dark).

## Ideabook detail page

The top bar shows the ideabook short name (aligned to right) and a back arrow (aligned to left) to navigate back to ideabooks list page.

User can click the ideabook short name to edit it in place.

The detail page is split to three tabs. The tab bar is fixed at the bottom. 
* Ideas
* Chat
* Notes

The top bar and tab bar color is the same color as the ideabook color.

### Ideas tab

Shows the list of ideas. Swipe to shows context menu:
* A delete button to delete a idea. 

The idea can be edited in place by tapping on the idea row. The edit mode support keyboard type input and voice input. User has the option to save or discard the edit.

The ideas can be reordered by drag-n-drop. When long click on an idea, it enters drag-n-drop mode.

There is a new idea button at the bottom of the page. It triggers the voice recording to create new idea.

### Chat tab

Look and feel like a common LLM chat UI. At bottom is a chat box ("Chat your ideabook"). With a mic icon to trigger voice chat. Messages are ordered from top to bottom. User message is shown aligned to the right with light gray background color. LLM response takes the full width of the screen without background.

User has the option to save a chat response as note, copy a chat response to clipboard, and clear chat history with a clear confirmation dialog before clearing. Cleared chat history is deleted perpermanently and can't be restored.

### Notes tab

A list of all saved notes. Each note shows a snippet of the note content. Swipe to shows context menu:
* A delete button to delete a note. 

Click on the Note to open the Note detail page.

The note detail page:
* The note title (user prompt) in gray background color. similar buble style as the user message in the chat tab. but takes the full width. User can tap on the buble to edit the note title in place. It suports voice and text input with options to save or discard the edit.
* The note text (LLM response) in Markdown format.
* Buttons area:
  ** Aligned to left: a "refresh" button to regenerate the note text by sending the note title (user prompt) to LLM again.
  ** Aligned to left: a "copy" button to copy the note text to clipboard.
  ** Aligned to right: a "delete" button to delete the entire note.

# Generative AI integration

In this doc, we use the term "Generative AI" and "LLM" interchangeably. LLM is used to transcribe voice recording to text, refine and summarize the text, and provide chat functions.

Voji uses Gemini API for all LLM needs. Different Gemini models are chosen for different use cases. The prompts, model names and generation configs are stored in Firebase remote config to fast iterate to improve user experience.

Gemini API Keys are securely stored in Firebase. User doesn't have direct access to the API keys from their client device.

LLM is used to power the following features:
* Transcribe voice recording to text.
* Refine and summarize the text.
* Automatically pick color for ideabook.
* Automatically suggest name for ideabook.
* Provide functions to allow user chat with their ideabooks.
* Provide suggested prompts for chat.
* Provide note refresh functions.

# Cloud integration

Voji mainly uses Firebase for cloud integration. Firebase provides the following services:
* Firebase Authentication for user authentication.
* Firebase Firestore for cloud data store and sync.
* Firebase Cloud Functions for serverless functions.
* Firebase Remote Config for remote config.
* Firebase Analytics for analytics.

## Firebase Firestore

Voji uses Firestore for cloud data store and sync.

Data model:

```
collection(users):
  doc(userId):
    p | string: user's passcode (encrypted) to lock / unlock ideabooks
    collection(ideabooks):
      doc(ideabookId):
        n | string: ideabook name
        c | single byte: ideabook color, encoded as single byte char.
        l | bool: ideabook is locked
        collection(ideas):
          doc(ideaId):
            c | string: idea content
            t | date: idea creation timestamp
        collection(notes):
          doc(noteId):
            t | string: note title
            c | string: note content
            r | date: note creation timestamp
```

For color encoding, follow the following map:

Color   | Byte Value (Hex) | Byte Value (Decimal)
--------|------------------|--------------------
red     | 0x00             | 0
green   | 0x01             | 1
blue    | 0x02             | 2
yellow  | 0x03             | 3
orange  | 0x04             | 4
purple  | 0x05             | 5

Voji uses Firestore streaming listener to listen to data changes and update data in real time.

# Appendix

## Algorithm: Hybrid Sort Order with Fractional Indexing for Ideas

The sorting order logic discussed in this section applies to both ideas and notes. But for the sake of simple, we will use "ideas" as the example.

User triggers drag-and-drop mode to reorder the ideas by long pressing on a idea row. When long press on a idea row, the row turns slightly bigger and brighter (depending on theme) to indicate the row is selected and ready to be draged for reordering. Long press on a idea row does not enter the idea edit mode.

**Objective:**
To provide a flexible sorting mechanism for a list of ideas within an ideabook. The system supports:
1.  **Default Sorting:** Ideas are sorted by creation time, with the newest idea appearing at the top.
2.  **Manual User Sorting:** Users can reorder ideas via drag-and-drop.
3.  **Efficiency:** Minimize Firestore write operations and data storage, primarily by only modifying the document of the idea being moved.

**Core Data Representation:**
Each idea document utilizes two potential values for determining its sort position:

1.  `created_at` (Timestamp): A standard Firestore metadata field indicating when the idea document was created (e.g., milliseconds since epoch). This is an immutable value and serves as the default sort key.
2.  `s` (Float): An optional field stored in the idea document. This field is only present if the idea has been manually reordered by the user. It stores a floating-point number representing its user-defined sort order.

**Effective Sort Value:**
To determine an idea's actual position in the sorted list, we use its "effective sort value":
*   `effective_sort_value(idea) = idea.s ?? idea.created_at`
    *(This means: if `idea.s` exists and is not null, use `idea.s`; otherwise, use `idea.created_at`.)*

**Sorting Rule:** Ideas are sorted in **descending order** based on their `effective_sort_value`. A higher value means the idea is ranked higher (appears closer to the top).

**Algorithm Details:**

1.  **Default State & New Ideas:**
    *   When an idea is newly created, it does **not** have an `s` field.
    *   Its `effective_sort_value` is its `created_at` timestamp.
    *   By default, the list is sorted by `created_at` descending, so new ideas appear at the top.

2.  **Manual Reordering (Drag-and-Drop):**
    When a user drags an idea (`moved_idea`) to a new position:
    *   The `s` field of `moved_idea` will be calculated and stored (or updated if it already exists). No other idea documents are modified.
    *   Let `idea_above` be the idea immediately above the new target position, and `idea_below` be the idea immediately below the new target position.

    *   **Case 1: Moved between two existing ideas (`idea_above` and `idea_below` both exist):**
        *   `s_new = (effective_sort_value(idea_above) + effective_sort_value(idea_below)) / 2.0`
        *   `moved_idea.s` is set to `s_new`.

    *   **Case 2: Moved to the absolute top of the list (`idea_above` does not exist):**
        *   Let `current_top_idea` be the idea that was previously at the top of the list (before this move).
        *   `s_new = effective_sort_value(current_top_idea) + 1.0`
        *   *(If the list was empty before moving this item, or if this is the only item being made top, `s_new` could default to its own `created_at + 1.0` or a predefined high value like `Date.now()`. The key is it must be higher than any other potential value. Using `previous_top_idea` is generally cleaner if the list wasn't empty).*
        *   `moved_idea.s` is set to `s_new`.

    *   **Case 3: Moved to the absolute bottom of the list (`idea_below` does not exist):**
        *   Let `current_bottom_idea` be the idea that was previously at the bottom of the list.
        *   `s_new = effective_sort_value(current_bottom_idea) - 1.0`
        *   *(Similar to the top case, if the list was empty, `s_new` could be its `created_at - 1.0` or a predefined low value. The key is it must be lower.)*
        *   `moved_idea.s` is set to `s_new`.

**Rationale:**

*   **Minimized Firestore Writes:** Only the `s` field of the single moved idea needs to be written to Firestore. This is cost-effective and performant.
*   **Minimized Storage:** Ideas that have never been manually reordered do not consume extra storage for an `s` field.
*   **Leverages Default Timestamps:** The `created_at` field provides a natural and free default sort order.
*   **Handles Edge Cases:** The `****` and `-1.0` logic for top/bottom placement creates clear separation and ample "space" for future insertions without immediately needing to average very close numbers.
*   **Resilient to Concurrent Edits (Partially):** While not fully conflict-free without more complex logic, if two users reorder different items, their changes are isolated to those items' `s` values. Collisions occur if they move items relative to the *same* neighbors simultaneously.

**Considerations & Limitations:**

*   **Floating-Point Precision:** When repeatedly inserting items between two specific items using the averaging method, the difference between their `s` values shrinks. Double-precision floats offer ~50-52 such "halving" operations in the absolute worst-case scenario (always inserting into the same narrowing gap) before precision limits prevent further distinct ordering within that specific gap. This is highly unlikely to be hit in typical user behavior for an ideabook.
*   **Client-Side Sorting:** The sorting logic (calculating effective sort values and performing the sort) is handled on the client. This is efficient for lists up to ~1000 items. For significantly larger lists, server-side assistance or more complex querying might be needed.

---

**Example Scenario:**

Assume `created_at` timestamps are simplified integers for clarity. Higher value = newer.

**Initial State (No `s` values yet):**
1.  Idea A (created_at: 1000) -> Effective: 1000 (Top)
2.  Idea B (created_at: 900)  -> Effective: 900
3.  Idea C (created_at: 800)  -> Effective: 800
4.  Idea D (created_at: 700)  -> Effective: 700 (Bottom)

**Displayed Order:** A, B, C, D

**Action 1: User drags Idea D between Idea A and Idea B.**
*   `moved_idea` = D
*   `idea_above` = A (effective_sort_value: 1000)
*   `idea_below` = B (effective_sort_value: 900)
*   D's new `s` value = (1000 + 900) / 2.0 = **950.0**
*   Firestore: Update Idea D, set `D.s = 950.0`

**New State & Effective Values:**
1.  Idea A (created_at: 1000)         -> Effective: 1000
2.  Idea D (created_at: 700, s: 950.0) -> Effective: 950.0
3.  Idea B (created_at: 900)          -> Effective: 900
4.  Idea C (created_at: 800)          -> Effective: 800

**Displayed Order:** A, D, B, C

**Action 2: User drags Idea C to the top of the list.**
*   `moved_idea` = C
*   `idea_above` = null (moving to top)
*   `current_top_idea` = A (effective_sort_value: 1000)
*   C's new `s` value = 1000 + 1.0 = **1001.0**
*   Firestore: Update Idea C, set `C.s = 1001.0`

**New State & Effective Values:**
1.  Idea C (created_at: 800, s: 1001.0) -> Effective: 1001.0
2.  Idea A (created_at: 1000)         -> Effective: 1000
3.  Idea D (created_at: 700, s: 950.0) -> Effective: 950.0
4.  Idea B (created_at: 900)          -> Effective: 900

**Displayed Order:** C, A, D, B

**Action 3: User drags Idea A to the bottom of the list.**
*   `moved_idea` = A
*   `idea_below` = null (moving to bottom)
*   `current_bottom_idea` = B (effective_sort_value: 900)
*   A's new `s` value = 900 - 1.0 = **899.0**
*   Firestore: Update Idea A, set `A.s = 899.0`

**New State & Effective Values:**
1.  Idea C (created_at: 800, s: 1001.0) -> Effective: 1001.0
2.  Idea D (created_at: 700, s: 950.0) -> Effective: 950.0
3.  Idea B (created_at: 900)          -> Effective: 900
4.  Idea A (created_at: 1000, s: 899.0) -> Effective: 899.0

**Displayed Order:** C, D, B, A

This refined description should provide a clear understanding of your sorting algorithm, its benefits, and how it works in practice.
```
